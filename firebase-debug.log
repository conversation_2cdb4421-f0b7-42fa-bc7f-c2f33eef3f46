[debug] [2025-06-08T13:05:25.643Z] ----------------------------------------------------------------------
[debug] [2025-06-08T13:05:25.646Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js deploy --only functions:updateTag
[debug] [2025-06-08T13:05:25.646Z] CLI Version:   14.1.0
[debug] [2025-06-08T13:05:25.646Z] Platform:      win32
[debug] [2025-06-08T13:05:25.646Z] Node Version:  v22.14.0
[debug] [2025-06-08T13:05:25.647Z] Time:          Sun Jun 08 2025 14:05:25 GMT+0100 (West Africa Standard Time)
[debug] [2025-06-08T13:05:25.647Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-08T13:05:25.906Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-08T13:05:25.906Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-08T13:05:25.906Z] [iam] checking project tradetracker-30ec1 for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]
[debug] [2025-06-08T13:05:25.908Z] Checked if tokens are valid: false, expires at: 1749388620174
[debug] [2025-06-08T13:05:25.908Z] Checked if tokens are valid: false, expires at: 1749388620174
[debug] [2025-06-08T13:05:25.908Z] > refreshing access token with scopes: []
[debug] [2025-06-08T13:05:25.910Z] >>> [apiv2][query] POST https://www.googleapis.com/oauth2/v3/token [none]
[debug] [2025-06-08T13:05:25.910Z] >>> [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-08T13:05:26.585Z] <<< [apiv2][status] POST https://www.googleapis.com/oauth2/v3/token 200
[debug] [2025-06-08T13:05:26.585Z] <<< [apiv2][body] POST https://www.googleapis.com/oauth2/v3/token [omitted]
[debug] [2025-06-08T13:05:26.600Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1:testIamPermissions [none]
[debug] [2025-06-08T13:05:26.600Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1:testIamPermissions x-goog-quota-user=projects/tradetracker-30ec1
[debug] [2025-06-08T13:05:26.601Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/tradetracker-30ec1:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}
